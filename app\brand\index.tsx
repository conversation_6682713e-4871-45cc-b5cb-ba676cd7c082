import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Animated, ScrollView, SafeAreaView, Dimensions, ActivityIndicator, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase.js';
import { useAuth } from '@/components/AuthContext'; // Import useAuth
import { addFavoriteBrand, removeFavoriteBrand, getFavoriteBrandIds } from '@/components/FavsContext'; // Import favorite functions
import Coupon, { CouponData } from '@/components/Coupon';

const { width } = Dimensions.get("window");

// Define interfaces for fetched data
interface BrandDetails {
  brand_id: number;
  displayed_name: string;
  logo: string;
  link: string;
  color: string; // Brand's main color
  savings_ft: number;
  discount: string; // Brand-level discount (e.g., "20%")
  activation: number; // Brand-level activation count
}

// Use the CouponData interface from the Coupon component
// BrandCouponData is now just an alias for CouponData

export default function BrandPage() {
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  const { brandId } = useLocalSearchParams();
  const [brand, setBrand] = useState<BrandDetails | null>(null);
  const [brandCoupons, setBrandCoupons] = useState<CouponData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Auth and Favorites Context
  const { employeeId, isLoadingAuth } = useAuth();
  const [userFavoriteBrandIds, setUserFavoriteBrandIds] = useState<number[]>([]);

  // activeFavourites state is no longer needed since coupon hearts reflect brand fav status
  // const [activeFavourites, setActiveFavourites] = useState<number[]>([]); // REMOVE THIS LINE

  // Initialize scaleAnim dynamically based on fetched coupons
  const [scaleAnim, setScaleAnim] = useState<Animated.Value[]>([]);

  // Toggle favorite for the brand itself (header heart and coupon hearts)
  const handleToggleBrandFavorite = useCallback(async () => {
    if (!employeeId) {
      Alert.alert("Authentication Required", "Please log in to favorite brands.");
      return;
    }

    if (!brand) return; // Should not happen if button is visible, but for type safety

    const brandIdToToggle = brand.brand_id;
    const isCurrentlyFavorited = userFavoriteBrandIds.includes(brandIdToToggle);

    let success = false;
    if (isCurrentlyFavorited) {
      success = await removeFavoriteBrand(employeeId, brandIdToToggle);
      if (success) {
        setUserFavoriteBrandIds(prev => prev.filter(id => id !== brandIdToToggle));
      } else {
        Alert.alert("Error", "Could not remove brand from favorites. Please try again.");
      }
    } else {
      success = await addFavoriteBrand(employeeId, brandIdToToggle);
      if (success) {
        setUserFavoriteBrandIds(prev => [...prev, brandIdToToggle]);
      } else {
        Alert.alert("Error", "Could not add brand to favorites. It might already be favorited or another error occurred.");
      }
    }
  }, [employeeId, userFavoriteBrandIds, brand]);

  // Wrapper function to match the expected onToggleFavorite signature
  const onToggleFavorite = useCallback(async (brandId: number): Promise<boolean> => {
    if (!employeeId) {
      Alert.alert("Authentication Required", "Please log in to favorite brands.");
      return false;
    }

    if (!brand) return false;

    const isCurrentlyFavorited = userFavoriteBrandIds.includes(brandId);
    let success = false;

    if (isCurrentlyFavorited) {
      success = await removeFavoriteBrand(employeeId, brandId);
      if (success) {
        setUserFavoriteBrandIds(prev => prev.filter(id => id !== brandId));
      } else {
        Alert.alert("Error", "Could not remove brand from favorites. Please try again.");
      }
    } else {
      success = await addFavoriteBrand(employeeId, brandId);
      if (success) {
        setUserFavoriteBrandIds(prev => [...prev, brandId]);
      } else {
        Alert.alert("Error", "Could not add brand to favorites. It might already be favorited or another error occurred.");
      }
    }

    return success;
  }, [employeeId, userFavoriteBrandIds, brand]);

  // Effect to fetch brand data and coupons
  useEffect(() => {
    async function fetchBrandData() {
      if (!brandId) {
        setIsLoading(false);
        setError("No brand ID provided.");
        return;
      }

      setIsLoading(true);
      setError(null);
      try {
        // Fetch brand details
        const { data: brandData, error: brandError } = await supabase
          .from('brand')
          .select('brand_id, displayed_name, logo, link, color, savings_ft, discount, activation')
          .eq('brand_id', parseInt(brandId as string))
          .single();

        if (brandError) {
          setError("Failed to load brand details.");
          setBrand(null);
          setBrandCoupons([]);
          return;
        }

        if (brandData) {
          setBrand(brandData as unknown as BrandDetails);

          // Fetch coupons for this brand
          const { data: couponsData, error: couponsError } = await supabase
            .from('coupon')
            .select(`
              coupon_id,
              discount,
              short_description,
              long_description,
              image,
              qr,
              special_offer,
              brand (
                brand_id,
                displayed_name,
                logo,
                link
              )
            `)
            .eq('brand_id', parseInt(brandId as string));

          if (couponsError) {
            setError("Failed to load brand coupons.");
            setBrandCoupons([]);
          } else {
            setBrandCoupons(
              (couponsData || []).map((coupon: any) => ({
                ...coupon,
                brand: Array.isArray(coupon.brand) ? coupon.brand[0] : coupon.brand,
              }))
            );
            // Initialize Animated.Value for each fetched coupon
            setScaleAnim(((couponsData || [])).map(() => new Animated.Value(1)));
          }
        } else {
          setError("Brand not found.");
          setBrand(null);
          setBrandCoupons([]);
        }
      } catch (err) {
        setError("An unexpected error occurred.");
        setBrand(null);
        setBrandCoupons([]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchBrandData();
  }, [brandId]); // Re-run effect if brandId changes

  // Effect to fetch user's favorite brands ONLY
  useEffect(() => {
    async function fetchUserFavorites() {
      if (employeeId && !isLoadingAuth) {
        try {
          const favorites = await getFavoriteBrandIds(employeeId);
          setUserFavoriteBrandIds(favorites);
        } catch (error) {
          // Handle error if needed
        }
      } else if (!employeeId && !isLoadingAuth) {
        setUserFavoriteBrandIds([]); // Clear brand favorites if user logs out
      }
    }
    fetchUserFavorites();
  }, [employeeId, isLoadingAuth]);

  if (isLoading || isLoadingAuth) { // Show loading while authenticating or fetching brand data
    return (
      <SafeAreaView style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text }]}>Loading data...</Text>
      </SafeAreaView>
    );
  }

  if (error || !brand) {
    return (
      <SafeAreaView style={[styles.errorContainer, { backgroundColor: theme.bg }]}>
        <Text style={[styles.errorText, { color: theme.text }]}>{error || "Brand data not available."}</Text>
        <TouchableOpacity style={{ marginTop: 20 }} onPress={() => router.back()}>
          <Text style={{ color: theme.primary, fontSize: 16 }}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  // Format dynamic data for display
  const formattedSavings = brand.savings_ft
    ? new Intl.NumberFormat('hu-HU', { style: 'currency', currency: 'HUF', maximumFractionDigits: 0 }).format(brand.savings_ft)
    : 'N/A Ft';

  const displayBrandDiscount = brand.discount !== undefined ? brand.discount.toString() : 'N/A';
  const displayBrandActivation = brand.activation !== undefined ? brand.activation.toString() : 'N/A';

  // This single variable determines the heart icon for both brand header and coupons
  const isBrandFavorited = userFavoriteBrandIds.includes(brand.brand_id);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
        {/* Felső sáv (Header) */}
        <View style={[styles.header, { backgroundColor: theme.tertiary }]}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={28} color={theme.primary} />
            </TouchableOpacity>
            {/* Brand logo next to arrow with dynamic background color */}
            {brand.logo ? (
              <View style={[styles.brandLogoBox, { backgroundColor: brand.color || 'white' }]}>
                <Image
                  source={{ uri: brand.logo }}
                  style={styles.brandLogo}
                />
              </View>
            ) : null}
          </View>
          {/* Szív ikon helye (Brand Favorite Heart) */}
          <TouchableOpacity onPress={handleToggleBrandFavorite}>
            <Ionicons name={isBrandFavorited ? "heart" : "heart-outline"} size={32} color={theme.primary} />
          </TouchableOpacity>
        </View>
      <ScrollView>

        {/* Nagy kép (Brand Main Image) */}
        {brand.logo ? (
          <Image source={{ uri: brand.logo }} style={styles.brandMainImage} />
        ) : (
          <View style={[styles.brandMainImage, { backgroundColor: theme.tertiary, justifyContent: 'center', alignItems: 'center' }]}>
            <Text style={{ color: theme.text, fontSize: 20 }}>No Brand Image</Text>
          </View>
        )}

        {/* Cím és leírás */}
        <View style={styles.titleBox}>
          <Text style={[styles.title, { color: theme.text }]}>{brand.displayed_name.toUpperCase()}</Text>
          <Text style={[styles.desc, { color: theme.text }]}>
            {brand.link}
          </Text>
        </View>

        {/* Statisztika szekció */}
        <View style={{ width: '100%', alignSelf: 'center', marginTop: 16, paddingHorizontal: 12 }}>
          <View style={[styles.statsBox, { backgroundColor: theme.bg, borderColor: theme.primary, borderWidth: 1.5 }]}>
            <Text style={[styles.statsBig, { color: theme.text }]}>{formattedSavings}</Text>
            <View style={[styles.statsLine, { backgroundColor: theme.primary }]} />
            <Text style={[styles.statsLabel, { color: theme.primary }]}>Spórolás</Text>
          </View>

          {/* Két kisebb doboz: Kedvezmény és Aktiválás */}
          <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: 8 }}>
            <View style={[styles.statsBoxSmall, { backgroundColor: theme.bg, borderColor: theme.primary, borderWidth: 1.5, marginRight: 8 }]}>
              <Text style={[styles.statsSmall, { color: theme.text }]}>{displayBrandDiscount}</Text>
              <Text style={[styles.statsLabel, { color: theme.primary }]}>Kedvezmény</Text>
            </View>
            <View style={[styles.statsBoxSmall, { backgroundColor: theme.bg, borderColor: theme.primary, borderWidth: 1.5, marginLeft: 8 }]}>
              <Text style={[styles.statsSmall, { color: theme.text }]}>{displayBrandActivation}</Text>
              <Text style={[styles.statsLabel, { color: theme.primary }]}>Aktiválás</Text>
            </View>
          </View>

          {/* Kedvezmények szekció (Coupons for this brand) */}
          <View style={styles.couponSection}>
            <Text style={[styles.couponTitle, { color: theme.text }]}>Kedvezmények</Text>
            {brandCoupons.length > 0 ? (
              <View style={styles.couponRow}>
                {brandCoupons.map((coupon, idx) => (
                  <View key={coupon.coupon_id} style={styles.couponWrapper}>
                    <Coupon
                      coupon={coupon}
                      userFavoriteBrandIds={userFavoriteBrandIds}
                      onToggleFavorite={onToggleFavorite}
                      scaleAnim={scaleAnim[idx]}
                      width={width * 0.44} // Slightly smaller for grid layout
                    />
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.noCouponsText, { color: theme.text }]}>No coupons available for this brand.</Text>
            )}
          </View>
        </View>

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 8,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  brandLogoBox: {
    borderRadius: 14,
    padding: 6,
    marginLeft: 2,
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    width: 48,
  },
  brandLogo: {
    width: 36,
    height: 36,
    resizeMode: 'contain',
  },
  brandMainImage: {
    width: "100%",
    height: 220,
    resizeMode: "cover",
    borderRadius: 0,
    marginTop: 0,
  },
  titleBox: {
    marginTop: 24,
    marginHorizontal: 24,
  },
  title: {
    fontWeight: "bold",
    fontSize: 26,
  },
  desc: {
    fontSize: 14,
    marginTop: 8,
  },
  statsBox: {
    borderRadius: 16,
    paddingVertical: 10,
    paddingHorizontal: 10,
    alignItems: "center",
    marginBottom: 0,
    position: "relative",
  },
  statsLine: {
    width: 40,
    height: 3,
    borderRadius: 2,
    marginTop: 6,
    marginBottom: 2,
  },
  statsBig: {
    fontSize: 28,
    fontWeight: "bold",
    marginTop: 10,
  },
  statsSmall: {
    fontSize: 22,
    fontWeight: "bold",
  },
  statsLabel: {
    fontSize: 14,
  },
  statsBoxSmall: {
    borderRadius: 16,
    padding: 20,
    alignItems: "center",
    flex: 1,
  },
  couponSection: {
    marginTop: 32,
    paddingHorizontal: 12,
  },
  couponTitle: {
    fontWeight: "bold",
    fontSize: 18,
    marginBottom: 8,
  },
  couponRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  couponWrapper: {
    width: "48%",
    marginBottom: 16,
  },

  noCouponsText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
  }
});