{"name": "everdeal", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/metro-runtime": "~6.1.2", "@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "2.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.50.3", "expo": "54.0.12", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~15.0.7", "expo-camera": "~17.0.8", "expo-constants": "~18.0.9", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-mail-composer": "~15.0.7", "expo-router": "~6.0.10", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.7", "expo-web-browser": "~15.0.8", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~4.1.1", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-native-web": "^0.21.0", "react-native-webview": "13.15.0", "react-native-worklets": "0.5.1"}, "devDependencies": {"@react-native-community/cli": "^20.0.2", "@react-native/metro-config": "^0.81.1", "@types/jest": "^29.5.12", "@types/react": "~19.1.10", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^30.0.2", "jest": "^29.2.1", "jest-expo": "~54.0.12", "typescript": "~5.9.2"}, "private": true}