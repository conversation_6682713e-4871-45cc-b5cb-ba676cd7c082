
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import QRCode from 'react-native-qrcode-svg';
import { useTheme } from '@/components/ThemeContext';
import { useAuth } from '@/components/AuthContext';
import { supabase } from '@/lib/supabase';

interface CouponData {
  coupon_id: number;
  discount: string;
  short_description: string;
  brand: {
    displayed_name: string;
  };
}

export default function QRCodePage() {
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();
  const { employeeId } = useAuth();
  const { couponId } = useLocalSearchParams();

  const [coupon, setCoupon] = useState<CouponData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCouponDetails();
  }, [couponId]);

  const fetchCouponDetails = async () => {
    if (!couponId) {
      //// setError("No coupon ID provided.");
      setIsLoading(false);
      return;
    }

    try {
      const { data, error: supabaseError } = await supabase
        .from('coupon')
        .select(`
          coupon_id,
          discount,
          short_description,
          brand (
            displayed_name
          )
        `)
        .eq('coupon_id', parseInt(couponId as string))
        .single();

      if (supabaseError) {
        //// setError("Failed to load coupon details.");
      } else {
        setCoupon({
          ...data,
          brand: Array.isArray(data.brand) ? data.brand[0] : data.brand
        });
      }
    } catch (err) {
      //// setError("An unexpected error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  const generateQRData = () => {
    if (!coupon || !employeeId) return '';
    
    const qrData = {
      userId: employeeId,
      couponId: coupon.coupon_id,
      timestamp: new Date().toISOString()
    };
    
    return JSON.stringify(qrData);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text }]}>Betöltés...</Text>
      </SafeAreaView>
    );
  }

  if (error || !coupon) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
        <Text style={[styles.errorText, { color: theme.text }]}>{error || "Coupon not found."}</Text>
        <TouchableOpacity style={{ marginTop: 20 }} onPress={() => router.back()}>
          <Text style={{ color: theme.primary, fontSize: 16 }}>Vissza</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.bg }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>QR Kód</Text>
        <View style={{ width: 24 }} />
      </View>

      <View style={styles.content}>
        <Text style={[styles.couponTitle, { color: theme.text }]}>
          {coupon.brand?.displayed_name}
        </Text>
        <Text style={[styles.couponDiscount, { color: theme.primary }]}>
          {coupon.discount}
        </Text>
        
        <View style={[styles.qrContainer, { backgroundColor: theme.bg }]}>
          <QRCode
            value={generateQRData()}
            size={250}
            color={theme.text}
            backgroundColor={theme.bg}
          />
        </View>
        
        <Text style={[styles.instructions, { color: theme.text }]}>
          Tartsd oda a szolgáltatónak ezt a QR kódot a kupon aktiválásához
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  couponTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  couponDiscount: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 40,
  },
  qrContainer: {
    padding: 20,
    borderRadius: 15,
    marginBottom: 30,
  },
  instructions: {
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});

