/**
 * Below are the themes that are used in the app.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

export type ThemeName = 'blue' | 'cyan' | 'white';

// Brand tokens — matches :root
const tokens = {
  cyan: '#40E0D0',
  blue: '#0E3154',
  darkCyan: '#007777',
  darkBlue: '#021329',
  white: '#FFFFFF',
  black: '#000000',
};

// Theme definitions — matches .theme-blue, .theme-cyan, etc.
export const themeValues: Record<ThemeName, {
  primary: string;
  secondary: string;
  tertiary: string;
  bg: string;
  text: string;
}> = {
  blue: {
    primary: tokens.cyan,
    secondary: tokens.darkCyan,
    tertiary: tokens.darkBlue,
    bg: tokens.blue,
    text: tokens.white,
  },
  cyan: {
    primary: tokens.cyan,
    secondary: tokens.darkBlue,
    tertiary: tokens.darkCyan,
    bg: '#C3F0EB',
    text: tokens.black,
  },
  white: {
    primary: tokens.cyan,
    secondary: tokens.darkCyan,
    tertiary: tokens.black,
    bg: tokens.white,
    text: tokens.black,
  },
};
