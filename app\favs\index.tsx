import React, { useState, useEffect, useCallback } from "react";
import { View, StyleSheet, ScrollView, Image, TouchableOpacity, Animated, Text, Dimensions, ActivityIndicator, Alert, Switch } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase.js';
import { useAuth } from '@/components/AuthContext';
import { addFavoriteBrand, removeFavoriteBrand, getFavoriteBrandIds } from '@/components/FavsContext';
import { useRouter } from "expo-router"; // Import useRouter

const { width } = Dimensions.get("window");

interface BrandData {
  brand_id: number;
  displayed_name: string;
  logo: string;
  color: string | null;
}

export default function FavsPage() {
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();
  const { employeeId, isLoadingAuth } = useAuth();
  const router = useRouter(); // Initialize router

  const [favoritedBrands, setFavoritedBrands] = useState<BrandData[]>([]);
  const [favoriteBrandIds, setFavoriteBrandIds] = useState<Set<number>>(new Set());
  const [instantUpdate, setInstantUpdate] = useState(true); // State for the switch
  const [allBrands, setAllBrands] = useState<BrandData[]>([]); // New state for all brands
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use a Map for Animated.Value instances for dynamic indexing
  const scaleAnims = React.useRef(new Map<number, Animated.Value>()).current;

  // Function to initialize or get Animated.Value for a specific brand_id
  const getScaleAnim = useCallback((brandId: number) => {
    if (!scaleAnims.has(brandId)) {
      scaleAnims.set(brandId, new Animated.Value(1));
    }
    return scaleAnims.get(brandId)!;
  }, [scaleAnims]);


  // Unified function to fetch both favorited and all brands
  const fetchBrandData = useCallback(async () => {
    if (isLoadingAuth) {
      // Still loading auth data, wait for it
      setIsLoading(true); // Keep loading state true while auth is loading
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // --- Fetch All Brands ---
      const { data: allBrandsData, error: allBrandsError } = await supabase
        .from('brand')
        .select('brand_id, displayed_name, logo, color');

      if (allBrandsError) {
        setError("Failed to load all brands.");
        setAllBrands([]);
        // Don't return, continue to fetch favorites
      } else {
        setAllBrands(allBrandsData || []);
      }

      // --- Fetch Favorited Brands (only if employeeId is available) ---
      if (employeeId) {
        const fetchedFavoriteIds = await getFavoriteBrandIds(employeeId);
        setFavoriteBrandIds(new Set(fetchedFavoriteIds));

        // Filter all brands data to get only the favorited ones
        const currentFavoritedBrands = (allBrandsData || []).filter(brand =>
          fetchedFavoriteIds.includes(brand.brand_id)
        );
        setFavoritedBrands(currentFavoritedBrands);
      } else {
        setFavoriteBrandIds(new Set());
        setFavoritedBrands([]); // Clear favorited brands if no user is logged in
      }

    } catch (err) {
      setError("An unexpected error occurred.");
      setAllBrands([]);
      setFavoritedBrands([]);
    } finally {
      setIsLoading(false);
    }
  }, [employeeId, isLoadingAuth]); // Re-run if employeeId or auth loading state changes

  // Effect to trigger fetching brand data
  useEffect(() => {
    fetchBrandData();
  }, [fetchBrandData]); // Dependency on the memoized fetch function

  // Function to handle toggling favorite status (add/remove) for any brand card
  const handleToggleFavorite = useCallback(async (brandIdToToggle: number) => {
    if (!employeeId) {
      Alert.alert("Authentication Required", "Please log in to manage favorites.");
      return false; // Indicate failure
    }

    // Trigger animation
    const animValue = getScaleAnim(brandIdToToggle);
    Animated.sequence([
      Animated.timing(animValue, { toValue: 1.3, duration: 120, useNativeDriver: true }),
      Animated.timing(animValue, { toValue: 1, duration: 120, useNativeDriver: true }),
    ]).start();

    const isCurrentlyFavorited = favoriteBrandIds.has(brandIdToToggle);

    let success = false;
    if (isCurrentlyFavorited) {
      success = await removeFavoriteBrand(employeeId, brandIdToToggle);
      if (success) {
        // Always update the ID set for instant heart icon feedback
        setFavoriteBrandIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(brandIdToToggle);
          return newSet;
        });
        // Conditionally update the list that controls rendering position
        if (instantUpdate) {
          setFavoritedBrands(prev => prev.filter(brand => brand.brand_id !== brandIdToToggle));
        }
      } else {
        Alert.alert("Error", "Could not remove brand from favorites. Please try again.");
      }
    } else {
      success = await addFavoriteBrand(employeeId, brandIdToToggle);
      if (success) {
        // Always update the ID set for instant heart icon feedback
        setFavoriteBrandIds(prev => {
          const newSet = new Set(prev);
          newSet.add(brandIdToToggle);
          return newSet;
        });
        // Conditionally update the list that controls rendering position
        if (instantUpdate) {
          const addedBrand = allBrands.find(brand => brand.brand_id === brandIdToToggle);
          if (addedBrand) {
            setFavoritedBrands(prev => [...prev, addedBrand]);
          }
        }
      } else {
        Alert.alert("Error", "Could not add brand to favorites. It might already be favorited or another error occurred.");
      }
    }
    return success;
  }, [employeeId, favoriteBrandIds, allBrands, getScaleAnim, instantUpdate]); // Dependencies for useCallback

  // Render a single brand card
  const renderBrandCard = (brand: BrandData, section: string) => {
    const isFavorited = favoriteBrandIds.has(brand.brand_id);
    const animValue = getScaleAnim(brand.brand_id);

    return (
      <TouchableOpacity
        key={`${section}-${brand.brand_id}`}
        style={[styles.card, { borderColor: theme.primary, backgroundColor: brand.color || '#ffffff' }]}
        onPress={() => router.push({ pathname: '/brand', params: { brandId: brand.brand_id.toString() } })}
        activeOpacity={0.8}
      >
        <Image
          source={{ uri: brand.logo }}
          style={styles.logo}
          resizeMode="contain"
        />
        <TouchableOpacity
          style={styles.heart}
          onPress={() => handleToggleFavorite(brand.brand_id)}
          hitSlop={10}
        >
          <Animated.View style={{ transform: [{ scale: animValue }] }}>
            <Ionicons
              name={isFavorited ? "heart" : "heart-outline"}
              size={32}
              color={theme.primary}
            />
          </Animated.View>
        </TouchableOpacity>
        <View style={[styles.cardBottom, { backgroundColor: theme.primary }]}>
          <Text style={[styles.cardBottomText, { color: theme.text }]}>
            {brand.displayed_name}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };


  if (isLoadingAuth || isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text }]}>
          {isLoadingAuth ? "Loading user data..." : "Loading brands..."}
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.bg }]}>
        <Text style={[styles.errorText, { color: theme.text }]}>{error}</Text>
        <TouchableOpacity style={{ marginTop: 20 }} onPress={fetchBrandData}>
          <Text style={{ color: theme.primary, fontSize: 16 }}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Filter out already favorited brands from the "discover" list
  const discoverBrands = allBrands.filter(
    brand => !favoritedBrands.some(favBrand => favBrand.brand_id === brand.brand_id)
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.bg }]}>
      {/* Switch for Instant Update */}
      <View style={[styles.switchContainer, { borderBottomColor: theme.tertiary }]}>
        <Text style={[styles.switchLabel, { color: theme.text }]}>
          Update lists instantly
        </Text>
        <Switch
          trackColor={{ false: theme.secondary, true: theme.primary }}
          thumbColor={theme.bg}
          ios_backgroundColor={theme.secondary}
          onValueChange={(value) => {
            setInstantUpdate(value);
            // When switching to instant update, synchronize the favorited brands list
            if (value) {
              const syncedFavoritedBrands = allBrands.filter(brand =>
                favoriteBrandIds.has(brand.brand_id)
              );
              setFavoritedBrands(syncedFavoritedBrands);
            }
          }}
          value={instantUpdate}
        />
      </View>

      {/* Favored Brands Section */}
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: theme.text }]}>Your Favorite Brands</Text>
      </View>
      {favoritedBrands.length === 0 ? (
        <View style={styles.emptySection}>
          <Ionicons name="heart-dislike-outline" size={60} color={theme.secondary} />
          <Text style={[styles.emptyText, { color: theme.text }]}>No favorites yet!</Text>
        </View>
      ) : (
        <View style={styles.grid}>
          {favoritedBrands.map(brand => renderBrandCard(brand, 'favorites'))}
        </View>
      )}

      {/* All Brands Section */}
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: theme.text }]}>Discover More Brands</Text>
      </View>
      {allBrands.length === 0 ? (
        <View style={styles.emptySection}>
          <Ionicons name="compass-outline" size={60} color={theme.secondary} />
          <Text style={[styles.emptyText, { color: theme.text }]}>No brands to display.</Text>
        </View>
      ) : discoverBrands.length === 0 ? (
        <View style={styles.emptySection}>
          <Ionicons name="checkmark-circle-outline" size={60} color={theme.secondary} />
          <Text style={[styles.emptyText, { color: theme.text }]}>You've favorited all available brands!</Text>
        </View>
      ) : (
        <View style={styles.grid}>
          {discoverBrands.map(brand => renderBrandCard(brand, 'discover'))}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
  },
  emptySubText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 10,
  },
  emptySection: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
    marginBottom: 20,
  },
  sectionHeader: {
    paddingHorizontal: 12,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  grid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    marginBottom: 20, // Add space after each grid
  },
  card: {
    backgroundColor: "#fff", // Card background remains white or a light color for contrast
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    alignItems: "center",
    justifyContent: "center",
    width: "48%", // Two cards per row with spacing
    height: 180,
    position: "relative",
    borderWidth: 3,
  },
  logo: {
    width: 80,
    height: 80,
    resizeMode: "contain",
    marginBottom: 20,
    marginTop: 6,
  },
  heart: {
    position: "absolute",
    top: 6,
    right: 6,
    borderRadius: 10,
    padding: 4,
  },
  cardBottom: { // Renamed from couponBottom for generality
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: "center",
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  cardBottomText: { // Renamed from couponBottomText
    fontSize: 16,
    textAlign: "center",
    fontWeight: 'bold',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    marginBottom: 10,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
});