
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { CameraView, Camera } from 'expo-camera';
import { useTheme } from '@/components/ThemeContext';
import { supabase } from '@/lib/supabase';

interface DecodedData {
  userId: string;
  couponId: string;
  timestamp: string;
  coupon?: {
    coupon_id: string;
    discount: string;
    short_description: string;
    brand: {
      displayed_name: string;
    };
  };
}

export default function QRDecodePage() {
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [decodedData, setDecodedData] = useState<DecodedData | null>(null);

  useEffect(() => {
    const getCameraPermissions = async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    };

    getCameraPermissions();
  }, []);

  const handleBarcodeScanned = async ({ data }: { type: string; data: string }) => {
    setScanned(true);
    
    try {
      const parsedData = JSON.parse(data);
      
      if (parsedData.userId && parsedData.couponId) {
        const { data: couponData, error } = await supabase
          .from('coupon')
          .select(`
            coupon_id,
            discount,
            short_description,
            brand!inner (
              displayed_name
            )
          `)
          .eq('coupon_id', parsedData.couponId)
          .single();

        if (error) {
          Alert.alert('Hiba', 'Nem sikerült betölteni a kupon adatokat');
          setScanned(false);
          return;
        }

        setDecodedData({
          ...parsedData,
          coupon: couponData
        });

        Alert.alert(
          'QR Kód Beolvasva',
          `Felhasználó: ${parsedData.userId}\nKupon: ${couponData.discount}`,
          [
            { text: 'Újra szkennelés', onPress: () => setScanned(false) },
            { text: 'OK' }
          ]
        );
      } else {
        Alert.alert('Hiba', 'Érvénytelen QR kód formátum');
        setScanned(false);
      }
    } catch (error) {
      Alert.alert('Hiba', 'Nem sikerült dekódolni a QR kódot');
      setScanned(false);
    }
  };

  if (hasPermission === null) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
        <Text style={[styles.text, { color: theme.text }]}>Kamera engedély kérése...</Text>
      </SafeAreaView>
    );
  }

  if (hasPermission === false) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
        <Text style={[styles.text, { color: theme.text }]}>Nincs kamera hozzáférés</Text>
      </SafeAreaView>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.bg }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>QR Szkennelés</Text>
        <View style={{ width: 24 }} />
      </View>

      <View style={styles.scannerContainer}>
        <CameraView
          style={StyleSheet.absoluteFillObject}
          facing="back"
          onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
        />
        
        <View style={styles.overlay}>
          <View style={styles.scanArea} />
          <Text style={[styles.instructions, { color: 'white' }]}>
            Helyezd a QR kódot a keretbe
          </Text>
        </View>
      </View>

      {decodedData && (
        <View style={[styles.resultContainer, { backgroundColor: theme.tertiary }]}>
          <Text style={[styles.resultTitle, { color: theme.text }]}>Beolvasott adatok:</Text>
          <Text style={[styles.resultText, { color: theme.text }]}>
            Felhasználó ID: {decodedData.userId}
          </Text>
          <Text style={[styles.resultText, { color: theme.text }]}>
            Brand: {decodedData.coupon?.brand?.displayed_name}
          </Text>
          <Text style={[styles.resultText, { color: theme.text }]}>
            Kupon: {decodedData.coupon?.discount}
          </Text>
          <Text style={[styles.resultText, { color: theme.text }]}>
            Időpont: {new Date(decodedData.timestamp).toLocaleString()}
          </Text>
        </View>
      )}

      {scanned && (
        <TouchableOpacity
          style={[styles.scanAgainButton, { backgroundColor: theme.primary }]}
          onPress={() => setScanned(false)}
        >
          <Text style={[styles.scanAgainText, { color: theme.text }]}>
            Újra szkennelés
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  scannerContainer: {
    flex: 1,
    position: 'relative',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  scanArea: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: 'white',
    backgroundColor: 'transparent',
    marginBottom: 20,
  },
  instructions: {
    fontSize: 16,
    textAlign: 'center',
  },
  resultContainer: {
    padding: 20,
    margin: 20,
    borderRadius: 10,
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 14,
    marginBottom: 5,
  },
  scanAgainButton: {
    margin: 20,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  scanAgainText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  text: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 50,
  },
});

