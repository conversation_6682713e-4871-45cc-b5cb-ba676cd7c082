import React, { useState, useEffect } from "react";
import { View, Text, Animated, Dimensions, StyleSheet, ScrollView, ActivityIndicator } from "react-native";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase.js'; // Adjust this path to your supabase client initialization
import Coupon, { CouponData } from '@/components/Coupon';

const { width } = Dimensions.get("window");
const couponWidth = width * 0.4;

// Define props interface for CouponsSection
interface CouponsSectionProps {
  userFavoriteBrandIds: number[]; // Array of brand IDs that are favorited
  onToggleFavorite: (brandId: number) => Promise<boolean>; // Callback to toggle favorite status
}

// Export as a default function component, accepting the defined props
export default function CouponsSection({ userFavoriteBrandIds, onToggleFavorite }: CouponsSectionProps) {
  const [coupons, setCoupons] = useState<CouponData[]>([]);
  const [topCouponsData, setTopCouponsData] = useState<CouponData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize scaleAnim with an empty array; it will be populated after data fetch
  const [scaleAnim, setScaleAnim] = useState<Animated.Value[]>([]);
  const [search, setSearch] = useState(""); // Assuming search functionality might be added later

  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  useEffect(() => {
    async function fetchCoupons() {
      setIsLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from('coupon')
          .select(`
            coupon_id,
            discount,
            short_description,
            long_description,
            image,
            qr,
            special_offer,
            brand (
              brand_id,
              displayed_name,
              logo,
              link
            )
          `);

        if (error) {
          setError("Failed to load coupons. Please try again later.");
          return;
        }

        if (data) {
          const regularOffers: CouponData[] = [];
          const specialOffers: CouponData[] = [];

          data.forEach((coupon: any) => {
            // Transform the coupon to handle brand as array from Supabase
            const transformedCoupon = {
              ...coupon,
              brand: Array.isArray(coupon.brand) ? coupon.brand[0] : coupon.brand,
            };

            if (coupon.special_offer) {
              specialOffers.push(transformedCoupon as CouponData);
            } else {
              regularOffers.push(transformedCoupon as CouponData);
            }
          });

          setCoupons(regularOffers);
          setTopCouponsData(specialOffers);

          // Initialize Animated.Value for each fetched coupon
          const allCoupons = [...regularOffers, ...specialOffers];
          setScaleAnim(allCoupons.map(() => new Animated.Value(1)));
        }
      } catch (err) {
        setError("An unexpected error occurred.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchCoupons();
  }, []); // Empty dependency array means this runs once on mount

  // Combine all coupons for the search filter and scaleAnim indexing
  const allDisplayCoupons = [...coupons, ...topCouponsData];

  const filteredCoupons = allDisplayCoupons.filter(
    (coupon) =>
      coupon.short_description.toLowerCase().includes(search.toLowerCase()) ||
      (coupon.brand && coupon.brand.displayed_name.toLowerCase().includes(search.toLowerCase()))
  );



  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text }]}>Loading coupons...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.bg }]}>
        <Text style={[styles.errorText, { color: theme.text }]}>{error}</Text>
      </View>
    );
  }

  return (
    <>
      <View style={styles.couponsContainer}>
        <Text style={[styles.couponsTitle, { color: theme.text }]}>Ajánlatok számodra</Text>
        <View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingRight: 8 }}>
            {coupons.map((coupon, idx) => (
              <View key={coupon.coupon_id} style={{ marginRight: 12 }}>
                <Coupon
                  coupon={coupon}
                  userFavoriteBrandIds={userFavoriteBrandIds}
                  onToggleFavorite={onToggleFavorite}
                  scaleAnim={scaleAnim[idx]}
                  width={couponWidth}
                />
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
      <View style={styles.couponsContainer}>
        <Text style={[styles.couponsTitle, { color: theme.text }]}>Heti top kedvezmények</Text>
        <View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingRight: 8 }}>
            {topCouponsData.map((coupon, idx) => (
              <View key={coupon.coupon_id} style={{ marginRight: 12 }}>
                <Coupon
                  coupon={coupon}
                  userFavoriteBrandIds={userFavoriteBrandIds}
                  onToggleFavorite={onToggleFavorite}
                  scaleAnim={scaleAnim[coupons.length + idx]}
                  width={couponWidth}
                />
              </View>
            ))}
          </ScrollView>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  couponsContainer: {
    paddingVertical: 12,
    marginLeft: 16,
    overflow: 'visible',
  },
  couponsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
});