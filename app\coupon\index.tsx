import React, { useRef, useEffect, useState, ComponentType, forwardRef, PropsWithoutRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Vibration,
  Animated,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase.js';

// Define the interface for the coupon data to ensure type safety
interface CouponData {
  coupon_id: number;
  discount: string;
  short_description: string;
  long_description: string;
  image: string;
  qr: string;
  special_offer: boolean;
  brand: {
    brand_id: number;
    displayed_name: string;
    logo: string;
    link: string;
    color: string;
    savings_ft: number;
    discount: string;
    activation: number;
  };
  // Add categories to the interface
  categories: string[];
}

const vibrationPattern = [1];
let vibrationInterval: number | null = null;

export default function CouponPage() {
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  const { couponId } = useLocalSearchParams();
  const [coupon, setCoupon] = useState<CouponData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const scrollY = useRef(new Animated.Value(0)).current;

  const imageScale = scrollY.interpolate({
    inputRange: [-180, 0],
    outputRange: [1.5, 1],
    extrapolate: 'clamp',
  });

  const imageTranslateY = scrollY.interpolate({
    inputRange: [0, 360],
    outputRange: [0, 180],
    extrapolate: 'clamp',
  });

  useEffect(() => {
    async function fetchCouponDetails() {
      if (!couponId) {
        setIsLoading(false);
        setError("No coupon ID provided.");
        return;
      }

      setIsLoading(true);
      setError(null);
      try {
        // Updated Supabase query to include categories
        const { data, error: supabaseError } = await supabase
          .from('coupon')
          .select(`
            *,
            brand ( * ),
            coupon_categories (
              categories ( category_name )
            )
          `)
          .eq('coupon_id', parseInt(couponId as string))
          .single();

        if (supabaseError) {
          console.error("Error fetching coupon details:", supabaseError);
          setError("Failed to load coupon details. Please try again.");
          setCoupon(null);
        } else if (data) {
          // Process the nested category data into a simple array of strings
          const processedData = {
            ...data,
            categories: data.coupon_categories.map((item: any) => item.categories.category_name),
          };
          setCoupon(processedData as unknown as CouponData);
        } else {
          setError("Coupon not found.");
          setCoupon(null);
        }
      } catch (err) {
        console.error("Unexpected error fetching coupon details:", err);
        setError("An unexpected error occurred.");
        setCoupon(null);
      } finally {
        setIsLoading(false);
      }
    }

    fetchCouponDetails();
  }, [couponId]);

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.loadingContainer, { backgroundColor: theme.bg }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[styles.loadingText, { color: theme.text }]}>Loading coupon...</Text>
      </SafeAreaView>
    );
  }

  if (error || !coupon) {
    return (
      <SafeAreaView style={[styles.errorContainer, { backgroundColor: theme.bg }]}>
        <Text style={[styles.errorText, { color: theme.text }]}>{error || "Coupon data not available."}</Text>
        <TouchableOpacity style={{ marginTop: 20 }} onPress={() => router.back()}>
          <Text style={{ color: theme.primary, fontSize: 16 }}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  const brandLogoBoxBackgroundColor = coupon.brand?.color || 'white';
  const formattedSavings = coupon.brand?.savings_ft
    ? new Intl.NumberFormat('hu-HU', { style: 'currency', currency: 'HUF', maximumFractionDigits: 0 }).format(coupon.brand.savings_ft)
    : 'N/A Ft';
  const displayActivation = coupon.brand?.activation !== undefined ? coupon.brand.activation.toString() : 'N/A';
  const displayBrandDiscount = coupon.brand?.discount !== undefined ? coupon.brand.discount.toString() : 'N/A';

  return (
    <View style={[styles.container, { backgroundColor: theme.bg }]}>
      {/* Fixed navigation header */}
      <View style={[styles.navigationHeaderBg, { backgroundColor: theme.tertiary }]}>
        <View style={styles.navigationHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={28} color={theme.primary} />
          </TouchableOpacity>
          {coupon.brand && coupon.brand.logo ? (
            <TouchableOpacity 
              style={[styles.brandLogoBox, { backgroundColor: brandLogoBoxBackgroundColor }]}
              onPress={() => {
                router.push({ pathname: "/brand", params: { brandId: coupon.brand.brand_id.toString() } });
              }}
            >
              <Image
                source={{ uri: coupon.brand.logo }}
                style={styles.brandLogo}
              />
            </TouchableOpacity>
          ) : null}
          <TouchableOpacity
            style={styles.cameraButton}
            onPress={() => router.push("/coupon/decode")}
          >
            <Ionicons name="camera" size={28} color={theme.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Scrollable content */}
      <Animated.ScrollView
        contentContainerStyle={styles.scrollContentContainer}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        scrollEventThrottle={16}
      >
        <View style={styles.imageContainer}>
          {coupon.image ? (
            <Animated.Image
              source={{ uri: coupon.image }}
              style={[
                styles.topImage,
                {
                  transform: [
                    { scale: imageScale },
                    { translateY: imageTranslateY },
                  ],
                },
              ]}
            />
          ) : (
            <Animated.View style={[styles.topImage, { backgroundColor: theme.tertiary, justifyContent: 'center', alignItems: 'center' }]}>
              <Text style={{ color: theme.text, fontSize: 20 }}>No Image</Text>
            </Animated.View>
          )}
        </View>
        <View style={[styles.cyanBanner, { backgroundColor: theme.primary }]}>
          <Text style={[styles.cyanBannerText, { color: theme.text }]}>{coupon.discount}</Text>
        </View>

        {/* --- CATEGORY TAGS DISPLAY --- */}
        {coupon.categories && coupon.categories.length > 0 && (
          <View style={styles.tagsContainer}>
            {coupon.categories.map((category, index) => (
              <View key={index} style={[styles.tag, { backgroundColor: theme.secondary }]}>
                <Text style={[styles.tagText, { color: theme.text }]}>{category}</Text>
              </View>
            ))}
          </View>
        )}
        {/* --- END OF CATEGORY TAGS --- */}

        <View style={[styles.card, { width: '92%', alignSelf: 'center', backgroundColor: theme.bg, borderColor: theme.primary }]}>
          <View style={styles.header}>
            <Text style={[styles.cardHeaderText, { color: theme.text }]}>Tudnivalók:</Text>
          </View>
          <Text style={[styles.cardText, { color: theme.text }]}>
            {coupon.long_description}
          </Text>
        </View>

        {/* Rest of your UI ... */}
        <View style={{ width: '92%', alignSelf: 'center', marginTop: 16 }}>
          <View style={{
            backgroundColor: theme.bg,
            borderRadius: 16,
            paddingVertical: 20,
            paddingHorizontal: 10,
            alignItems: "center",
            marginBottom: 0,
            borderWidth: 1.5,
            borderColor: theme.primary,
            position: "relative",
          }}>
            <Text style={{ color: theme.text, fontSize: 32, fontWeight: "bold", marginTop: 10 }}>{formattedSavings}</Text>
            <View style={{
              width: 40,
              height: 3,
              backgroundColor: theme.primary,
              borderRadius: 2,
              marginTop: 6,
              marginBottom: 2,
            }} />
            <Text style={{ color: theme.primary, fontSize: 14, marginBottom: 2 }}>Spórolás</Text>
          </View>
        </View>
        <View style={{ width: '92%', alignSelf: 'center', marginTop: 8 }}>
          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <View style={{
              backgroundColor: theme.bg,
              borderRadius: 16,
              padding: 20,
              alignItems: "center",
              flex: 1,
              marginRight: 8,
              borderWidth: 1.5,
              borderColor: theme.primary,
            }}>
              <Text style={{ color: theme.text, fontSize: 22, fontWeight: "bold" }}>
                {displayBrandDiscount}
              </Text>
              <Text style={{ color: theme.primary, fontSize: 14 }}>Kedvezmény</Text>
            </View>
            <View style={{
              backgroundColor: theme.bg,
              borderRadius: 16,
              padding: 20,
              alignItems: "center",
              flex: 1,
              marginLeft: 8,
              borderWidth: 1.5,
              borderColor: theme.primary,
            }}>
              <Text style={{ color: theme.text, fontSize: 22, fontWeight: "bold" }}>{displayActivation}</Text>
              <Text style={{ color: theme.primary, fontSize: 14 }}>Aktiválás</Text>
            </View>
          </View>
        </View>
        {coupon.brand && coupon.brand.link ? (
          <View style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 50,
            marginTop: 20,
          }}>
            <TouchableOpacity
              style={[styles.brandButton, { backgroundColor: theme.secondary }]}
              activeOpacity={0.85}
              onPress={() => {
                router.push({ pathname: "/brand", params: { brandId: coupon.brand.brand_id.toString() } });
              }}
            >
              <Text style={[styles.brandButtonText, { color: theme.text }]}>Márka megtekintése</Text>
              <Ionicons name="open-outline" size={22} color={theme.text} style={{ marginLeft: 8 }} />
            </TouchableOpacity>
          </View>
        ) : null}
        <View style={{ height: 64 }} />
      </Animated.ScrollView>

      {/* Fixed bottom button */}
      <View style={styles.fixedButtonContainer}>
        <TouchableOpacity
          style={[styles.fixedButton, { backgroundColor: theme.primary, flexDirection: 'row', alignItems: 'center', justifyContent: 'center', position: 'relative' }]}
          activeOpacity={0.85}
          onPress={() => {
            router.push({ pathname: "/coupon/code", params: { couponId: coupon.coupon_id.toString() } });
          }}
          onPressIn={() => {
            Vibration.vibrate(vibrationPattern, true);
            vibrationInterval ??= setInterval(() => {
              Vibration.vibrate(vibrationPattern, true);
            }, 1);
          }}
          onPressOut={() => {
            Vibration.cancel();
            if (vibrationInterval) {
              clearInterval(vibrationInterval);
              vibrationInterval = null;
            }
          }}
        >
          {/* Arrow at far left, absolutely positioned */}
          <View style={{ position: 'absolute', left: 16, top: 0, bottom: 0, justifyContent: 'center' }}>
            <View style={{
              backgroundColor: theme.text,
              borderRadius: 999,
              alignItems: 'center',
              justifyContent: 'center',
              width: 50,
              height: 50,
            }}>
              <Ionicons name="arrow-forward" size={36} color={theme.primary} />
            </View>
          </View>
          {/* Centered text */}
          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <Text style={[styles.fixedButtonText, { color: theme.text }]}>Felhasználom</Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

// ... withRotate HOC remains the same

const styles = StyleSheet.create({
  // ... existing styles
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
  },
  card: {
    borderRadius: 12,
    padding: 20,
    marginTop: 8, // Adjusted from marginBottom: 20
    marginBottom: 8,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeaderText: {
    fontWeight: "bold",
    fontSize: 20,
  },
  cardText: {
    fontSize: 18,
    lineHeight: 28,
  },
  header: {
    alignItems: "flex-start",
    marginBottom: 16,
  },
  navigationHeaderBg: {
    width: '100%',
    paddingTop: 0,
    paddingBottom: 0,
  },
  navigationHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 8,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 8,
  },
  cameraButton: {
    padding: 8,
  },
  brandLogoBox: {
    borderRadius: 14,
    padding: 6,
    marginLeft: 2,
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    width: 48,
  },
  brandLogo: {
    width: 36,
    height: 36,
    resizeMode: 'contain',
  },
  imageContainer: {
    width: "100%",
    alignItems: "center",
    marginTop: 0,
    marginBottom: 0,
  },
  topImage: {
    width: "100%",
    height: 180,
    resizeMode: "cover",
    borderRadius: 0,
  },
  cyanBanner: {
    width: "92%",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    marginTop: 10,
    marginBottom: 0, // Adjusted from 8 to make space for tags
    borderRadius: 999,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  cyanBannerText: {
    fontSize: 24,
    fontWeight: "bold",
    letterSpacing: 1,
  },
  scrollContentContainer: {
    padding: 0,
    paddingBottom: 32,
  },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingBottom: 18,
    zIndex: 10,
  },
  fixedButton: {
    width: '92%',
    height: 64,
    borderRadius: 999,
    paddingVertical: 6,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  fixedButtonText: {
    fontSize: 22,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  brandButton: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 18,
  },
  brandButtonText: {
    fontWeight: "bold",
    fontSize: 16,
    marginRight: 4,
  },
  // --- New Styles for Category Tags ---
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    paddingHorizontal: '4%', // Aligns with the 92% width of other elements
    marginTop: 12,
    marginBottom: 4,
    gap: 8,
  },
  tag: {
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 14,
  },
  tagText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
