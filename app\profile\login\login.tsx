import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useTheme } from "@/components/ThemeContext";
import { H1, H2, Body } from "@/components/ThemedTypography";
import { useTypography } from "@/hooks/useTypography";
import { supabase } from "@/lib/supabase";
import { useRouter } from "expo-router";

export default function LoginPage() {
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  const [email, setEmail] = useState("");
  const [aszfChecked, setAszfChecked] = useState(false);
  const [adatChecked, setAdatChecked] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [codeSent, setCodeSent] = useState(false);
  const [code, setCode] = useState("");

  const canContinue = email.length > 0 && aszfChecked && adatChecked;

  // Send OTP code to email
  const handleSendCode = async () => {
    setLoading(true);
    setError("");
    try {
      const { error: otpError } = await supabase.auth.signInWithOtp({
        email,
        options: { shouldCreateUser: true },
      });
      if (otpError) {
        setError(otpError.message);
        Alert.alert("Hiba", otpError.message, [{ text: "OK" }]);
      } else {
        setCodeSent(true);
        Alert.alert("Kód elküldve", "Ellenőrző kód elküldve az e-mail címre.", [{ text: "OK" }]);
      }
    } catch (err) {
      setError("Hiba történt a kód küldése során. Kérjük, próbálja újra.");
    } finally {
      setLoading(false);
    }
  };

  // Verify OTP code
  const handleVerifyCode = async () => {
    setLoading(true);
    setError("");
    try {
      const { error: verifyError } = await supabase.auth.verifyOtp({
        email,
        token: code,
        type: "email",
      });
      if (verifyError) {
        setError(verifyError.message);
        Alert.alert("Hiba", verifyError.message, [{ text: "OK" }]);
      } else {
        Alert.alert("Sikeres bejelentkezés", "Sikeresen bejelentkeztél!", [{ text: "OK" }]);
        router.replace("/home");
      }
    } catch (err) {
      setError("Hiba történt a kód ellenőrzése során. Kérjük, próbálja újra.");
    } finally {
      setLoading(false);
    }
  };

  function CustomCheckbox({ value, onValueChange }: Readonly<{ value: boolean; onValueChange: (newValue: boolean) => void }>) {
    return (
      <TouchableOpacity
        onPress={() => onValueChange(!value)}
        style={{
          width: 24,
          height: 24,
          borderWidth: 2,
          borderColor: value ? theme.primary : theme.text,
          backgroundColor: value ? theme.primary : "transparent",
          borderRadius: 6,
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {value && (
          <Text style={{ color: theme.text, fontSize: 18, fontWeight: "bold", lineHeight: 20 }}>✓</Text>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoid}
      >
        <View style={styles.innerContainer}>
          {!codeSent && (
            <View style={{ justifyContent: "center", alignItems: "center", marginBottom: 8 }}>
              <Image
                source={require('../../../assets/images/EverDeal-Logo-White-110.png')}
                style={{ width: 240, height: 50 }}
                resizeMode="contain"
              />
            </View>
          )}
          <View style={styles.formContainer}>
            <H1 color="text" style={{ textAlign: "center", marginBottom: 24 }}>Bejelentkezés</H1>
            <View style={styles.inputContainer}>
              <H2 color="text">E-mail cím</H2>
              <TextInput
                style={[styles.input, { color: "#000" }]}
                placeholder="E-mail cím megadása..."
                placeholderTextColor="#aaa"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                editable={!loading}
              />
            </View>
            {codeSent && (
              <View style={styles.inputContainer}>
                <H2 color="text">6 jegyű kód</H2>
                <TextInput
                  style={[styles.input, { color: "#000" }]}
                  placeholder="Kód az e-mailből..."
                  placeholderTextColor="#aaa"
                  value={code}
                  onChangeText={setCode}
                  keyboardType="number-pad"
                  editable={!loading}
                  maxLength={6}
                />
                <TouchableOpacity
                  style={[styles.button, { backgroundColor: theme.secondary, marginTop: 8 }]}
                  onPress={handleSendCode}
                  disabled={loading}
                >
                  <Body color="text">Új kód küldése</Body>
                </TouchableOpacity>
              </View>
            )}
            {error ? (
              <View style={styles.errorContainer}>
                <Body color="#ff0000" style={{ textAlign: "center" }}>
                  {error}
                </Body>
              </View>
            ) : null}
            <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 8 }}>
              <CustomCheckbox value={aszfChecked} onValueChange={setAszfChecked} />
              <Body color="text" style={{ marginLeft: 8 }}>Elfogadom az ÁSZF-et</Body>
            </View>
            <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 16 }}>
              <CustomCheckbox value={adatChecked} onValueChange={setAdatChecked} />
              <Body color="text" style={{ marginLeft: 8 }}>
                Hozzájárulok adataim statisztikai célokra történő felhasználásához
              </Body>
            </View>
            {!codeSent ? (
              <TouchableOpacity
                style={[styles.button, canContinue && !loading ? { backgroundColor: theme.primary } : { backgroundColor: theme.secondary }]}
                onPress={handleSendCode}
                disabled={!canContinue || loading}
              >
                {loading ? <Body color="text">Küldés...</Body> : <H1 color="text">Kód küldése</H1>}
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.button, code.length === 6 && !loading ? { backgroundColor: theme.primary } : { backgroundColor: theme.secondary }]}
                onPress={handleVerifyCode}
                disabled={code.length !== 6 || loading}
              >
                {loading ? <Body color="text">Ellenőrzés...</Body> : <H1 color="text">Bejelentkezés</H1>}
              </TouchableOpacity>
            )}
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: "#0A233C",
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  keyboardAvoid: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: "flex-start",
  },
  formContainer: {
    marginTop: 40, // Reduced margin to move content higher
  },
  errorContainer: {
    backgroundColor: "rgba(255, 68, 68, 0.1)",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 68, 68, 0.3)",
  },

  inputContainer: {
    marginBottom: 20,
  },

  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: "#f9f9f9",
  },
  button: {
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },

});
