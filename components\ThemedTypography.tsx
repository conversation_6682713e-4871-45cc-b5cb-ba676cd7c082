import React from 'react';
import { Text, type TextProps, StyleSheet } from 'react-native';
import { useTheme } from '@/components/ThemeContext';
import { useFont } from '@/components/FontContext';
import { FontStyleName } from '@/constants/Fonts';

export type ThemedTypographyProps = TextProps & {
  variant?: FontStyleName;
  color?: 'primary' | 'secondary' | 'tertiary' | 'text' | 'bg' | string;
  customColor?: string; // For custom colors not in theme
};

export function ThemedTypography({
  style,
  variant = 'body',
  color = 'text',
  customColor,
  children,
  ...rest
}: ThemedTypographyProps) {
  const { getCustomTheme } = useTheme();
  const { getFontStyle } = useFont();
  const theme = getCustomTheme();
  
  // Get font style based on variant
  const fontStyle = getFontStyle(variant);
  
  // Determine text color
  let textColor: string;
  if (customColor) {
    textColor = customColor;
  } else if (color in theme) {
    textColor = theme[color as keyof typeof theme];
  } else {
    textColor = color; // Assume it's a direct color value
  }

  return (
    <Text
      style={[
        {
          fontSize: fontStyle.fontSize,
          lineHeight: fontStyle.lineHeight,
          fontWeight: fontStyle.fontWeight as any,
          fontFamily: fontStyle.fontFamily,
          letterSpacing: fontStyle.letterSpacing,
          color: textColor,
        },
        style,
      ]}
      {...rest}
    >
      {children}
    </Text>
  );
}

// Convenience components for common typography variants
export const H1 = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="h1" {...props} />
);

export const H2 = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="h2" {...props} />
);

export const H3 = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="h3" {...props} />
);
/*
*
export const H4 = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="h4" {...props} />
);

export const H5 = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="h5" {...props} />
);

export const H6 = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="h6" {...props} />
);
*/
export const Body = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="body" {...props} />
);
/*
*
export const BodySmall = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="bodySmall" {...props} />
);

export const Caption = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="caption" {...props} />
);

export const ButtonText = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="button" {...props} />
);

export const Overline = (props: Omit<ThemedTypographyProps, 'variant'>) => (
  <ThemedTypography variant="overline" {...props} />
);
*/