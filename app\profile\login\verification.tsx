import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/components/ThemeContext"; // Import your useTheme hook
import { supabase } from "@/lib/supabase";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from "@/components/AuthContext";

// Generate a 6-digit verification code
const generateVerificationCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send verification email automatically
/**
const sendVerificationEmail = async (email: string, code: string): Promise<boolean> => {
  try {
    // In a real production app, you would call your backend API here
    // Example: await fetch('/api/send-verification-email', { method: 'POST', body: JSON.stringify({ email, code }) })

    // For development/demo purposes, we'll simulate automatic sending
    const now = new Date();
    const timestamp = `${String(now.getFullYear()).slice(-2)}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}. ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    // Format code as xxx-xxx
    const codeWithDash = code.length === 6 ? code.slice(0, 3) + '-' + code.slice(3) : code;
    console.log(`[${timestamp}] Verification email sent to ${email} with code: ${code} (${codeWithDash})`);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // In production, this would return the actual API response
    return true; // Simulate successful sending

  } catch (error) {
    console.error('Error sending verification email:', error);
    return false;
  }
};
*/

export default function VerificationPage() {
  const router = useRouter();
  const { refreshAuth } = useAuth();
  const { email, verificationCode: expectedCode, codeGeneratedAt: receivedGeneratedAt, savedEmail, savedAszf, savedAdat } = useLocalSearchParams<{
    email?: string;
    verificationCode?: string;
    codeGeneratedAt?: string;
    savedEmail?: string;
    savedAszf?: string;
    savedAdat?: string;
  }>();
  const [timer, setTimer] = useState(15 * 60); // 15 minutes in seconds
  const [resending, setResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0); // seconds
  const [resendCooldownId, setResendCooldownId] = useState<NodeJS.Timeout | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [codeExpired, setCodeExpired] = useState(false);
  const [codeGeneratedAt, setCodeGeneratedAt] = useState<Date | null>(null);

  const { getCustomTheme } = useTheme(); // Access the custom theme hook
  const theme = getCustomTheme(); // Get the current theme's values

  // Store the current verification code
  const [currentVerificationCode, setCurrentVerificationCode] = useState(expectedCode || "");

  // Initialize code generation time when component mounts
  React.useEffect(() => {
    if (receivedGeneratedAt) {
      // Use the received generation time
      setCodeGeneratedAt(new Date(receivedGeneratedAt));
    } else if (expectedCode && !codeGeneratedAt) {
      // If we have a code but no generation time, assume it was just generated
      setCodeGeneratedAt(new Date());
    }
  }, [expectedCode, receivedGeneratedAt, codeGeneratedAt]);

  // Generate a 6-digit verification code
  const generateVerificationCode = (): string => {
    return Math.floor(100000 + Math.random() * 900000).toString();
  };

  // Send verification email automatically
  const sendVerificationEmail = async (email: string, code: string): Promise<boolean> => {
    try {
      // In a real production app, you would call your backend API here
      // Example: await fetch('/api/send-verification-email', { method: 'POST', body: JSON.stringify({ email, code }) })

      // For development/demo purposes, we'll simulate automatic sending
      const now = new Date();
      const timestamp = `${String(now.getFullYear()).slice(-2)}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}. ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      const codeWithDash = code.length === 6 ? code.slice(0, 3) + '-' + code.slice(3) : code;
      console.log(`[${timestamp}] Verification email sent to ${email} with code: ${codeWithDash}`);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In production, this would return the actual API response
      return true; // Simulate successful sending

    } catch (error) {
      console.error('Error sending verification email:', error);
      Alert.alert(
        "Email küldési hiba",
        "Hiba történt az email küldése során. Kérjük, próbálja újra.",
        [{ text: "OK" }]
      );
      return false;
    }
  };

  // Update timer every second with real-time calculation
  React.useEffect(() => {
    if (!codeGeneratedAt) return;

    const updateTimer = () => {
      const now = new Date();
      const elapsedMs = now.getTime() - codeGeneratedAt.getTime();
      const elapsedSeconds = Math.floor(elapsedMs / 1000);
      const remainingSeconds = Math.max(0, (15 * 60) - elapsedSeconds);

      setTimer(remainingSeconds);
    };

    // Update immediately
    updateTimer();

    // Set up interval to update every second
    const id = setInterval(updateTimer, 1000);

    return () => {
      if (id) clearInterval(id);
    };
  }, [codeGeneratedAt]); // Only depend on codeGeneratedAt

  // Separate effect to handle expiration status
  React.useEffect(() => {
    if (timer === 0 && codeGeneratedAt) {
      setCodeExpired(true);
    } else if (timer > 0) {
      setCodeExpired(false);
    }
  }, [timer, codeGeneratedAt]);

  const handleResend = async () => {
    if (!email) return;

    setResending(true);

    try {
      // Generate new verification code
      const newCode = generateVerificationCode();

      // Send verification email
      const emailSent = await sendVerificationEmail(email, newCode);

      if (emailSent) {
        // Update the current verification code and reset generation time
        setCurrentVerificationCode(newCode);
        setCodeGeneratedAt(new Date()); // Reset the generation time
        setCodeExpired(false); // Reset expiration status

        setResending(false);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 5000); // Hide after 5s

        // Start resend cooldown
        setResendCooldown(10); // 10 seconds cooldown
        if (resendCooldownId) clearInterval(resendCooldownId);
        const cooldownId = setInterval(() => {
          setResendCooldown((prev) => {
            if (prev <= 1) {
              clearInterval(cooldownId as any);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        setResendCooldownId(cooldownId as any);
        setVerificationCode(""); // Clear the input field
      } else {
        setResending(false);
        Alert.alert(
          "Hiba",
          "Nem sikerült újraküldeni az ellenőrző kódot. Kérjük, próbálja újra.",
          [{ text: "OK" }]
        );
      }
    } catch (error) {
      setResending(false);
      Alert.alert(
        "Hiba",
        "Hiba történt az ellenőrző kód újraküldése során.",
        [{ text: "OK" }]
      );
    }
  };

  // Handler for input change
  const handleCodeChange = (text: string) => {
    // Remove dash if user deletes back to 2 or fewer digits
    let cleaned = text.replace(/[^0-9]/g, "");
    if (cleaned.length > 6) cleaned = cleaned.slice(0, 6);
    if (cleaned.length > 3) {
      setVerificationCode(cleaned.slice(0, 3) + '-' + cleaned.slice(3));
    } else {
      setVerificationCode(cleaned);
    }
  };

  const minutes = String(Math.floor(timer / 60)).padStart(2, "0");
  const seconds = String(timer % 60).padStart(2, "0");
  const cooldownSeconds = String(resendCooldown).padStart(2, "0");

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.bg }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoid}
      >
        <View style={styles.innerContainer}>
          {/* Success Popup */}
          {showSuccess && (
            <View style={styles.successPopup} pointerEvents="none">
              <Text style={styles.successText}>Code sent successfully!</Text>
            </View>
          )}

          {/* Back Arrow */}
          <TouchableOpacity
            style={{ alignSelf: "flex-start", marginBottom: 16 }}
            onPress={() => router.replace({
              pathname: "/profile/login/login",
              params: {
                savedEmail: savedEmail || email || "",
                savedAszf: savedAszf || "false",
                savedAdat: savedAdat || "false",
                verificationCode: currentVerificationCode, // Pass back the verification code
                codeGeneratedAt: codeGeneratedAt?.toISOString() || "" // Pass back the generation time
              }
            })}
          >
            <Ionicons name="arrow-back" size={28} color={theme.primary} />
          </TouchableOpacity>
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              marginBottom: 8,
            }}
          >
            <Image
              source={require('../../../assets/images/EverDeal-Logo-White-110.png')}
              style={{ width: 240, height: 50 }}
              resizeMode="contain"
            />
          </View>
          <Text style={[styles.subtitle, { color: theme.text }]}>Email Verification</Text>

          {/* Show the email info if present */}
          {email && (
            <Text style={[styles.infoText, { color: theme.text }]}>
              Please give the 6 digit verification code sent to <Text style={{ color: theme.primary }}>&quot;{email}&quot;</Text>. This helps us keeping your account secured.
            </Text>
          )}

          <Text style={[styles.infoText, { color: theme.text }]}>
            Did not got the code? Click on the "Resend" button.
          </Text>

          {/* Timer */}
          {timer > 0 ? (
            <Text style={[styles.codeAvailableText, { color: theme.text }]}>
              Code is available for <Text style={{ color: 'red' }}>{minutes}:{seconds}</Text>
            </Text>
          ) : (
            <Text style={[styles.codeAvailableText, { color: 'red' }]}>
              Code has expired. Please request a new code.
            </Text>
          )}

          {/* Resend Button */}
          <TouchableOpacity
            style={[
              styles.resendButton,
              { borderColor: theme.primary, backgroundColor: "transparent" },
              (resendCooldown > 0 || resending) && { opacity: 0.5 },
            ]}
            onPress={handleResend}
            disabled={resendCooldown > 0 || resending}
          >
            <Text style={[styles.resendButtonText, { color: theme.primary }]}>
              {resending
                ? "Resending..."
                : resendCooldown > 0
                  ? `Resend available in ${cooldownSeconds}`
                  : "Resend"}
            </Text>
          </TouchableOpacity>

          <TextInput
            style={[styles.input, { color: "#000" }]}
            placeholder="Verification code"
            placeholderTextColor="#aaa"
            keyboardType="number-pad"
            maxLength={7} // 6 digits + 1 dash
            value={verificationCode}
            onChangeText={handleCodeChange}
          />

          <TouchableOpacity
            style={[styles.button, { backgroundColor: theme.primary }]}
            onPress={async () => {
              // Check if the code has expired
              if (codeExpired || timer === 0) {
                Alert.alert(
                  "Kód lejárt",
                  "Az ellenőrző kód lejárt. Kérjük, kérjen új kódot a 'Resend' gombbal.",
                  [{ text: "OK" }]
                );
                return;
              }

              // Check if entered code matches the expected code
              const enteredCode = verificationCode.replace(/\D/g, ''); // Remove non-digits
              const expectedCodeClean = currentVerificationCode.replace(/\D/g, ''); // Remove non-digits

              if (enteredCode === expectedCodeClean && enteredCode.length === 6) {
                // Code is correct, now authenticate with Supabase
                try {
                  if (!email) {
                    Alert.alert("Hiba", "E-mail cím hiányzik.");
                    return;
                  }

                  // Get the employee data to find the employee_id
                  const { data: employeeData, error: employeeError } = await supabase
                    .from('employee')
                    .select('employee_id, email, name')
                    .eq('email', email.trim().toLowerCase())
                    .single();

                  if (employeeError || !employeeData) {
                    Alert.alert("Hiba", "Nem sikerült betölteni a felhasználói adatokat.");
                    return;
                  }

                  // Since we're using a custom verification flow, we'll create a simple session
                  // by storing the employee data locally using AsyncStorage

                  try {
                    // Create a custom session object
                    const customSession = {
                      employee_id: employeeData.employee_id,
                      email: employeeData.email,
                      name: employeeData.name,
                      authenticated: true,
                      login_time: new Date().toISOString()
                    };

                    // Store the session in AsyncStorage
                    await AsyncStorage.setItem('employee_session', JSON.stringify(customSession));

                    // Refresh the AuthContext to pick up the new session
                    await refreshAuth();

                  } catch (error) {
                    console.error('Authentication error:', error);
                    Alert.alert("Hiba", "Nem sikerült bejelentkezni. Kérjük, próbálja újra.");
                    return;
                  }

                  // Authentication successful, proceed to home
                  router.replace({ pathname: "/home" });

                } catch (error) {
                  console.error('Verification error:', error);
                  Alert.alert("Hiba", "Hiba történt a bejelentkezés során. Kérjük, próbálja újra.");
                }
              } else {
                // Code is incorrect
                Alert.alert(
                  "Hibás kód",
                  "Az ellenőrző kód helytelen. Kérjük, ellenőrizze és próbálja újra.",
                  [{ text: "OK" }]
                );
              }
            }}
          >
            <Text style={[styles.buttonText, { color: theme.text }]}>Verify</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: "#0A233C",
  },
  keyboardAvoid: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    padding: 24,
    justifyContent: "center",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
    //color: "#fff",
  },
  subtitle: {
    fontSize: 18,
    //color: "#fff",
    marginBottom: 32,
    textAlign: "center",
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
    //color: "#fff",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: "#f9f9f9",
    //color: "#000",
    marginBottom: 12,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },
  buttonText: {
    //color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  infoText: {
    //color: "#fff",
    fontSize: 16,
    marginBottom: 12,
    textAlign: "center",
  },
  /*
  timerText: {
    color: "#40E0D0",
    fontSize: 20,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 8,
  },
  */
  resendButton: {
    borderWidth: 2,
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: "center",
    marginBottom: 16,
  },
  resendButtonText: {
    fontSize: 16,
    fontWeight: "bold",
  },
  codeAvailableText: {
    //color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  successPopup: {
    position: 'absolute',
    top: 40,
    left: 0,
    right: 0,
    zIndex: 100,
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignSelf: 'center',
    marginHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 10,
  },
  successText: {
    color: '#00ff00',
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
});
