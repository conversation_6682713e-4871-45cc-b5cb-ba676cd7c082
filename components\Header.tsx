import { Ionicons } from "@expo/vector-icons";
import { useRouter, useSegments, router, Tabs } from "expo-router";
import { Pressable, Text, StyleSheet, Platform, View, TextInput, TouchableOpacity, Image } from "react-native";
import  { useState, useEffect } from "react";
import { useTheme } from "@/components/ThemeContext";
import SearchOverlay from "@/components/SearchOverlay";

interface HeaderProps {
  onSearchChange?: (searchTerm: string) => void;
  searchResultCount?: number;
  userFavoriteBrandIds?: number[];
  onToggleFavorite?: (brandId: number) => Promise<boolean>;
}

export default function TabLayout({ 
  onSearchChange, 
  searchResultCount = 0, 
  userFavoriteBrandIds = [], 
  onToggleFavorite 
}: Readonly<HeaderProps>) {
  const searchIcon = Platform.OS === "ios" ? "search-outline" : "search";
  const closeIcon = Platform.OS === "ios" ? "close-outline" : "close";
  const homeIcon = Platform.OS === "ios" ? "home-outline" : "home";
  const pricetagIcon = Platform.OS === "ios" ? "pricetag-outline" : "pricetag";

  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  const [search, setSearch] = useState("");
  const [searchVisible, setSearchVisible] = useState(false);
  const router = useRouter();
  const segments = useSegments() as string[];
  const hideSearch = segments.includes("map") || segments.includes("profile");

  useEffect(() => {
    if (hideSearch && searchVisible) {
      setSearchVisible(false);
      setSearch("");
      onSearchChange?.("");
    }
  }, [hideSearch, searchVisible, onSearchChange]);

  const handleSearchPress = () => {
    setSearchVisible(true);
  };

  const handleSearchChange = (text: string) => {
    setSearch(text);
    onSearchChange?.(text);
  };

  const handleClosePress = () => {
    setSearchVisible(false);
    setSearch("");
    onSearchChange?.("");
  };

  return (
    <>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          padding: 10,
          backgroundColor: theme.tertiary,
          zIndex: 1001, // Ensure header stays above overlay
        }}
      >
        {/* Logo or Search Input */}
        {searchVisible ? (
          <>
            <TextInput
              style={{
                flex: 1,
                height: 40,
                backgroundColor: theme.bg,
                borderRadius: 8,
                paddingHorizontal: 12,
                color: theme.text,
                fontSize: 16,
                marginRight: 10,
              }}
              placeholder="Keresés..."
              placeholderTextColor={theme.text + "80"}
              value={search}
              onChangeText={handleSearchChange}
              autoFocus={true}
            />
            {/* Search result counter */}
            {search.trim() && (
              <View
                style={{
                  backgroundColor: theme.primary,
                  borderRadius: 12,
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  marginRight: 10,
                  minWidth: 24,
                  alignItems: 'center',
                }}
              >
                <Text
                  style={{
                    color: theme.bg,
                    fontSize: 12,
                    fontWeight: 'bold',
                  }}
                >
                  {searchResultCount > 0 ? `1/${searchResultCount}` : '0/0'}
                </Text>
              </View>
            )}
          </>
        ) : (
          <>
            <Image
              source={require('../assets/images/EverDeal-Logo-White-110.png')}
              style={{ width: 192, height: 40 }}
              resizeMode="contain"
            />
            {!hideSearch && <View style={{ flex: 1 }} />}
          </>
        )}

        {/* Search/Close icon */}
        {!hideSearch && (
          <Pressable
            onPress={searchVisible ? handleClosePress : handleSearchPress}
            style={({ pressed }) => ({
              opacity: pressed ? 0.5 : 1,
              marginRight: 10,
            })}
          >
            <Ionicons 
              name={searchVisible ? closeIcon : searchIcon} 
              size={36} 
              color={theme.primary} 
            />
          </Pressable>
        )}
      </View>
      
      <SearchOverlay 
        visible={searchVisible} 
        onClose={handleClosePress}
        userFavoriteBrandIds={userFavoriteBrandIds}
        onToggleFavorite={onToggleFavorite}
      />
    </>
  );
}
