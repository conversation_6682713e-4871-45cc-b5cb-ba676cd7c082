import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";
import Constants from "expo-constants";

// Load environment variables
const supabaseUrl = "https://egxsgyhiysoqbnnrcxvp.supabase.co";
//  process.env.SUPABASE_URL || Constants.expoConfig?.extra?.supabaseUrl;
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVneHNneWhpeXNvcWJubnJjeHZwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyMTQ4OTUsImV4cCI6MjA2MTc5MDg5NX0.geXG8HJxF6-UwJAqtD80BKh0vXFEcvC3r3h1RYOx1J0";
//  process.env.SUPABASE_ANON_KEY || Constants.expoConfig?.extra?.supabaseAnonKey;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    "Supabase credentials not found. Please check your .env file or app config."
  );
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
