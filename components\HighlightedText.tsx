import React from 'react';
import { Text, TextStyle, View } from 'react-native';
import { useTheme } from '@/components/ThemeContext';

interface HighlightedTextProps {
  text: string;
  searchTerm: string;
  style?: any;
  highlightStyle?: any;
  showCounter?: boolean;
  counterStyle?: any;
  currentMatchIndex?: number;
  globalMatchStartIndex?: number; // New prop to indicate where this component's matches start in global count
}

export default function HighlightedText({ 
  text, 
  searchTerm, 
  style, 
  highlightStyle,
  showCounter = false,
  counterStyle,
  currentMatchIndex = 1,
  globalMatchStartIndex = 1
}: Readonly<HighlightedTextProps>) {
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  if (!searchTerm.trim()) {
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <Text style={style}>{text}</Text>
        {showCounter && (
          <Text style={[{ color: theme.secondary, fontSize: 12, marginLeft: 8 }, counterStyle]}>
            0/0
          </Text>
        )}
      </View>
    );
  }

  const parts = text.split(new RegExp(`(${searchTerm})`, 'gi'));
  let matchCount = 0;
  
  return (
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      <Text style={style}>
        {parts.map((part, index) => {
          if (part.toLowerCase() === searchTerm.toLowerCase()) {
            matchCount++;
            const globalMatchNumber = globalMatchStartIndex + matchCount - 1;
            const isCurrentMatch = globalMatchNumber === currentMatchIndex;
            return (
              <Text 
                key={index} 
                style={[
                  { 
                    backgroundColor: isCurrentMatch ? '#FFD700' : '#ffd900bb', // Gold for current, semi-transparent for others
                    color: isCurrentMatch ? '#000000' : theme.text, // Black text for current match
                    borderRadius: 3,
                    paddingHorizontal: 2,
                    borderWidth: isCurrentMatch ? 2 : 0,
                    borderColor: isCurrentMatch ? '#FF6B35' : 'transparent' // Orange border for current
                  },
                  highlightStyle
                ]}
              >
                {part}
              </Text>
            );
          }
          return part;
        })}
      </Text>
    </View>
  );
}





