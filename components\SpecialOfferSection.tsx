import React, { useRef, useEffect, useState } from "react";
import { View, Animated, ScrollView, Image, Text, Dimensions, StyleSheet, ActivityIndicator, TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { useTheme } from "@/components/ThemeContext";
import { supabase } from '@/lib/supabase.js';

const { width } = Dimensions.get("window");

// Define the interface for the special offer data fetched from Supabase
interface SpecialOfferData {
  coupon_id: number;
  discount: string;
  short_description: string;
  long_description: string;
  image: string; // URL for the coupon image
  qr: string; // URL for the QR code
  special_offer: boolean;
  brand: {
    brand_id: number;
    displayed_name: string;
    logo: string; // URL for the brand logo
    link: string;
    color: string; // Brand's main color
  };
}

export default function SpecialOfferSection() {
  const [specialOffers, setSpecialOffers] = useState<SpecialOfferData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const scrollX = useRef(new Animated.Value(0)).current;
  const adScrollRef = useRef<ScrollView>(null);
  const router = useRouter();
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();

  // Fetch special offers from Supabase
  useEffect(() => {
    async function fetchSpecialOffers() {
      setIsLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from('coupon')
          .select(`
            coupon_id,
            discount,
            short_description,
            long_description,
            image,
            qr,
            special_offer,
            brand (
              brand_id,
              displayed_name,
              logo,
              link,
              color
            )
          `)
          .eq('special_offer', true); // Only fetch coupons where special_offer is true

        if (error) {
          setError("Failed to load special offers. Please try again later.");
          return;
        }

        if (data) {
          // Transform the data to handle brand as array from Supabase
          const transformedData = data.map((offer: any) => ({
            ...offer,
            brand: Array.isArray(offer.brand) ? offer.brand[0] : offer.brand,
          }));
          setSpecialOffers(transformedData as SpecialOfferData[]);
        }
      } catch (err) {
        setError("An unexpected error occurred.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchSpecialOffers();
  }, []);

  // Auto-scroll effect
  useEffect(() => {
    if (specialOffers.length === 0) return;

    let currentIndex = 0;
    const interval = setInterval(() => {
      currentIndex = (currentIndex + 1) % specialOffers.length;
      adScrollRef.current?.scrollTo({ x: currentIndex * width, animated: true });
    }, 4000); // 4000ms for a 4-second interval
    return () => clearInterval(interval);
  }, [specialOffers]);

  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.adCarouselWrapper, { justifyContent: 'center', alignItems: 'center', height: 200 }]}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={[{ color: theme.text, marginTop: 10 }]}>Loading special offers...</Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.adCarouselWrapper, { justifyContent: 'center', alignItems: 'center', height: 200 }]}>
        <Text style={[{ color: theme.text, textAlign: 'center' }]}>{error}</Text>
      </View>
    );
  }

  // No special offers available
  if (specialOffers.length === 0) {
    return (
      <View style={[styles.adCarouselWrapper, { justifyContent: 'center', alignItems: 'center', height: 200 }]}>
        <Text style={[{ color: theme.text, textAlign: 'center' }]}>No special offers available at the moment.</Text>
      </View>
    );
  }

  return (
    <View style={styles.adCarouselWrapper}>
      <Animated.ScrollView
        ref={adScrollRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {specialOffers.map((offer) => (
          <TouchableOpacity
            key={offer.coupon_id}
            style={styles.adBox}
            activeOpacity={0.9}
            onPress={() => router.push({ pathname: "/coupon", params: { couponId: offer.coupon_id.toString() } })}
          >
            <Image source={{ uri: offer.image }} style={styles.adImage} />
            <View style={styles.adTextContainer}>
              <View style={styles.textBackground}>
                <View style={styles.titleContainer}>
                  <Text style={styles.adTitle}>{offer.short_description}</Text>
                  <View style={[styles.discountBadge, { backgroundColor: theme.primary }]}>
                    <Text style={[styles.discountText, { color: theme.text }]}>{offer.discount}</Text>
                  </View>
                </View>
                <Text style={styles.adDesc}>{offer.long_description}</Text>
              </View>
            </View>
            <View style={[styles.adLogoContainer, { backgroundColor: offer.brand.color || '#ffffff' }]}>
              <Image source={{ uri: offer.brand.logo }} style={styles.adLogo} />
            </View>
          </TouchableOpacity>
        ))}
      </Animated.ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  adCarouselWrapper: {
    alignItems: "center",
    marginTop: 8,
    marginBottom: 0,
  },
  adBox: {
    backgroundColor: "#000",
    width: width * 0.92,
    borderRadius: 24,
    marginHorizontal: width * 0.04,
    marginBottom: 8,
    overflow: "hidden",
    alignItems: "flex-start",
    justifyContent: "flex-end",
    padding: 0,
    height: 200,
  },
  adImage: {
    width: "100%",
    height: "100%",
    position: "absolute",
    borderRadius: 24,
  },
  textBackground: {
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 18,
    paddingTop: 18,
    paddingBottom: 12,
  },
  adTextContainer: {
    position: "absolute",
    left: 0,
    top: 0,
    right: 0,
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 4,
  },
  adTitle: {
    color: "#fff",
    fontSize: 17,
    fontWeight: "bold",
    flex: 1,
    marginRight: 10,
  },
  adDesc: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "400",
  },
  adLogoContainer: {
    position: "absolute",
    right: 18,
    bottom: 18,
    borderRadius: 12,
    padding: 8,
    justifyContent: "center",
    alignItems: "center",
    width: 48,
    height: 48,
  },
  adLogo: {
    width: 32,
    height: 32,
    resizeMode: "contain",
  },
  discountBadge: {
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
    minWidth: 50,
    alignItems: "center",
    alignSelf: "flex-start",
  },
  discountText: {
    fontSize: 14,
    fontWeight: "bold",
  },
});
