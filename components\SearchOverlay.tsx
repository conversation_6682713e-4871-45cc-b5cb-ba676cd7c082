import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Pressable, ScrollView, Text, Animated, Dimensions, ActivityIndicator, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '@/components/ThemeContext';
import { supabase } from '@/lib/supabase.js';
import Coupon, { CouponData } from '@/components/Coupon';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get("window");
const couponWidth = width * 0.44;

interface BrandData {
  brand_id: number;
  displayed_name: string;
  logo: string;
  link: string;
  color: string;
  savings_ft: number;
  discount: string;
  activation: number;
}

interface SearchOverlayProps {
  visible: boolean;
  onClose: () => void;
  userFavoriteBrandIds?: number[];
  onToggleFavorite?: (brandId: number) => Promise<boolean>;
}

export default function SearchOverlay({ 
  visible, 
  onClose, 
  userFavoriteBrandIds = [], 
  onToggleFavorite 
}: SearchOverlayProps) {
  const [coupons, setCoupons] = useState<CouponData[]>([]);
  const [brands, setBrands] = useState<BrandData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scaleAnim, setScaleAnim] = useState<Animated.Value[]>([]);
  
  const { getCustomTheme } = useTheme();
  const theme = getCustomTheme();
  const router = useRouter();

  useEffect(() => {
    if (visible) {
      fetchData();
    }
  }, [visible]);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch coupons
      const { data: couponsData, error: couponsError } = await supabase
        .from('coupon')
        .select(`
          coupon_id,
          discount,
          short_description,
          long_description,
          image,
          qr,
          special_offer,
          brand (
            brand_id,
            displayed_name,
            logo,
            link
          )
        `);

      // Fetch brands
      const { data: brandsData, error: brandsError } = await supabase
        .from('brand')
        .select(`
          brand_id,
          displayed_name,
          logo,
          link,
          color,
          savings_ft,
          discount,
          activation
        `);

      if (couponsError || brandsError) {
        setError("Failed to load data");
        return;
      }

      if (couponsData) {
        const transformedCoupons = couponsData.map((coupon: any) => ({
          ...coupon,
          brand: Array.isArray(coupon.brand) ? coupon.brand[0] : coupon.brand,
        }));
        setCoupons(transformedCoupons);
        setScaleAnim(transformedCoupons.map(() => new Animated.Value(1)));
      }

      if (brandsData) {
        setBrands(brandsData);
      }
    } catch (err) {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleFavorite = async (brandId: number) => {
    if (onToggleFavorite) {
      return await onToggleFavorite(brandId);
    }
    return false;
  };

  const renderBrandCard = (brand: BrandData) => (
    <TouchableOpacity
      key={brand.brand_id}
      style={[styles.brandCard, { borderColor: theme.primary, backgroundColor: brand.color || theme.bg }]}
      onPress={() => {
        onClose();
        router.push({ pathname: '/brand', params: { brandId: brand.brand_id.toString() } });
      }}
      activeOpacity={0.8}
    >
      <Image
        source={{ uri: brand.logo }}
        style={styles.brandLogo}
        resizeMode="contain"
      />
      <View style={[styles.brandCardBottom, { backgroundColor: theme.primary }]}>
        <Text style={[styles.brandName, { color: theme.text }]} numberOfLines={1}>
          {brand.displayed_name}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (!visible) return null;

  return (
    <Pressable style={styles.overlay} onPress={onClose}>
      <Pressable style={styles.content} onPress={(e) => e.stopPropagation()}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={[styles.loadingText, { color: theme.text }]}>Loading...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: theme.text }]}>{error}</Text>
          </View>
        ) : (
          <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
            {/* Coupons Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: theme.text }]}>All Coupons ({coupons.length})</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.horizontalScrollContent}
              >
                {coupons.map((coupon, idx) => (
                  <View key={coupon.coupon_id} style={styles.couponWrapper}>
                    <Coupon
                      coupon={coupon}
                      userFavoriteBrandIds={userFavoriteBrandIds}
                      onToggleFavorite={handleToggleFavorite}
                      scaleAnim={scaleAnim[idx]}
                      width={couponWidth}
                      onPress={() => {
                        onClose();
                        router.push({ pathname: "/coupon", params: { couponId: coupon.coupon_id.toString() } });
                      }}
                    />
                  </View>
                ))}
              </ScrollView>
            </View>

            {/* Brands Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: theme.text }]}>All Brands ({brands.length})</Text>
              <View style={styles.brandsGrid}>
                {brands.map(renderBrandCard)}
              </View>
            </View>
          </ScrollView>
        )}
      </Pressable>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 100,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000000dd',
    zIndex: 999,
  },
  content: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  horizontalScrollContent: {
    paddingRight: 16,
  },
  couponWrapper: {
    marginRight: 12,
  },
  brandsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  brandCard: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    overflow: 'hidden',
  },
  brandLogo: {
    flex: 1,
    width: '100%',
    padding: 16,
  },
  brandCardBottom: {
    padding: 8,
    alignItems: 'center',
  },
  brandName: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  brandDiscount: {
    fontSize: 12,
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

