// C:\Users\<USER>\Desktop\asztal\BME\Startup\code\App_demo\components\AuthContext.tsx
import React, { createContext, useState, useEffect, useContext, useMemo } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface CustomSession {
  employee_id: string;
  email: string;
  name: string;
  authenticated: boolean;
  login_time: string;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  employeeId: string | null;
  isLoadingAuth: boolean;
  customSession: CustomSession | null;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [employeeId, setEmployeeId] = useState<string | null>(null);
  const [customSession, setCustomSession] = useState<CustomSession | null>(null);
  const [isLoadingAuth, setIsLoadingAuth] = useState(true); // Should start as true

  // Function to get employee_id from the user metadata or employee table
  const getEmployeeId = async (authUser: User): Promise<string | null> => {
    try {
      // First check if the user metadata contains employee_id (for our custom auth)
      if (authUser.user_metadata?.employee_id) {
        return authUser.user_metadata.employee_id;
      }

      // If user has an email and it's not anonymous, query the employee table
      if (authUser.email && !authUser.is_anonymous) {
        const { data, error } = await supabase
          .from('employee')
          .select('employee_id')
          .eq('email', authUser.email)
          .single();

        if (error || !data) {
          console.error('Error fetching employee_id:', error);
          return null;
        }

        return data.employee_id;
      }

      // For anonymous users, check if they have employee metadata
      if (authUser.user_metadata?.is_employee && authUser.user_metadata?.employee_id) {
        return authUser.user_metadata.employee_id;
      }

      return null;
    } catch (error) {
      console.error('Error in getEmployeeId:', error);
      return null;
    }
  };

  // Function to load custom session from AsyncStorage
  const loadCustomSession = async (): Promise<CustomSession | null> => {
    try {
      const sessionData = await AsyncStorage.getItem('employee_session');
      if (sessionData) {
        return JSON.parse(sessionData);
      }
    } catch (error) {
      console.error('Error loading custom session:', error);
    }
    return null;
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // First check for custom session
        const customSessionData = await loadCustomSession();
        if (customSessionData && customSessionData.authenticated) {
          setCustomSession(customSessionData);
          setEmployeeId(customSessionData.employee_id);
          setIsLoadingAuth(false);
          return;
        }

        // If no custom session, check Supabase session
        const { data: { session } } = await supabase.auth.getSession();
        setSession(session);
        setUser(session?.user || null);

        if (session?.user) {
          const empId = await getEmployeeId(session.user);
          setEmployeeId(empId);
        } else {
          setEmployeeId(null);
        }

        setIsLoadingAuth(false);
      } catch (error) {
        console.error('Error initializing auth:', error);
        setIsLoadingAuth(false);
      }
    };

    initializeAuth();

    // Still listen for Supabase auth changes in case they're used elsewhere
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (_event, session) => {
      // Only update if we don't have a custom session
      const customSessionData = await loadCustomSession();
      if (!customSessionData || !customSessionData.authenticated) {
        setSession(session);
        setUser(session?.user || null);

        if (session?.user) {
          const empId = await getEmployeeId(session.user);
          setEmployeeId(empId);
        } else {
          setEmployeeId(null);
        }

        setIsLoadingAuth(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []); // Empty dependency array means this runs once on mount

  const signOut = async () => {
    try {
      // Clear custom session
      await AsyncStorage.removeItem('employee_session');
      setCustomSession(null);
      setEmployeeId(null);

      // Also sign out from Supabase if there's a session
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const refreshAuth = async () => {
    try {
      const customSessionData = await loadCustomSession();
      if (customSessionData && customSessionData.authenticated) {
        setCustomSession(customSessionData);
        setEmployeeId(customSessionData.employee_id);
      }
    } catch (error) {
      console.error('Error refreshing auth:', error);
    }
  };

  const value = useMemo(() => ({
    session,
    user,
    employeeId,
    isLoadingAuth,
    customSession,
    signOut,
    refreshAuth,
  }), [session, user, employeeId, isLoadingAuth, customSession]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};